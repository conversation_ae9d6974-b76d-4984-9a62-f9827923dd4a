package adhoc.logging_monitoring

import adhoc.base.BaseAdhocITSpec
import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Level
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import java.util.concurrent.TimeUnit
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@SpringBootTest(
classes = [BcmonitoringApplication.class],
webEnvironment = SpringBootTest.WebEnvironment.NONE
)
@ActiveProfiles("test")
class LoggingAndMonitoringITSpec extends BaseAdhocITSpec {

	@Autowired
	ApplicationContext applicationContext

	@MockitoSpyBean
	Web3jConfig web3jConfig

	@Override
	Web3jConfig getWeb3jConfig() {
		return web3jConfig
	}

	def setupSpec() {
		setupSpecCommon()
	}

	def cleanupSpec() {
		cleanupSpecCommon()
	}

	def setup() {
		setupCommon()
		// Upload real ABI files to S3
		AdhocHelper.uploadHardhatAbiFiles(s3Client, TEST_BUCKET, "3000", [
			"AccessCtrl",
			"Token",
			"Account",
			"Provider"
		])
	}

	def cleanup() {
		cleanupCommon()
	}

	/**
	 * Should log include relevant context fields in StructuredLoggingContext
	 * Verifies service correctly logs with StructuredLoggingContext
	 * Expected: Logs include fields: event_name, trace_id, tx_hash, block_height, log_index, block_timestamp
	 */
	def "Should log include relevant context fields in StructuredLoggingContext"() {
		given: "Blocks and Events"
		// Setup new block events: 3 blocks with different event types
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 1000L],
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 3)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		// Setup pending events: 2 role-related events from earlier block
		def pendingEvents = ["roleGranted", "roleRevoked"]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown"
		noExceptionThrown()

		and: "Logs include fields like tx_hash, block_height, event_name, trace_id"
		assert logAppender.list.any() {
			it.getMDCPropertyMap().get("event_name") == "AddProviderRole" &&
					it.getMDCPropertyMap().get("tx_hash") == "0xabc123134" &&
					it.getMDCPropertyMap().get("block_height") == "1000" &&
					it.getMDCPropertyMap().get("trace_id") == "" &&
					it.getMDCPropertyMap().get("block_timestamp") == "**********" &&
					it.getMDCPropertyMap().get("log_index") == "0"
		}
	}

	/**
	 * Should appropriate log levels used for different scenarios
	 * Verifies service uses appropriate log levels for different scenarios and correct behaviors
	 * Expected:
	 * - WARN for non-critical issues (block delay) - service continues operation
	 * - ERROR for critical errors (block height = 0) - service stops and restarts
	 * - INFO for normal operations - service processes successfully
	 */
	def "Should appropriate log levels used for different scenarios"() {
		given: "Blocks and Events with mixed scenarios"
		// Setup new block events: mix of normal and error scenarios
		def newBlockEvents = [
			[logType: 'addProviderRole', txHash: '0xabc123', blockNumber: 0L],
			// ERROR scenario: block height = 0
			[logType: 'addTokenByProvider', txHash: '0xdef456', blockNumber: 1001L],
			// Normal scenario
			[logType: 'roleAdminChanged', txHash: '0x123abc', blockNumber: 1002L]    // Normal scenario
		]
		def mockNotifications = createMockNewHeadsNotifications(1000L, 5)
		setUpEventStream(mockNotifications)
		setupMockWeb3jWithEvents(newBlockEvents)

		// Setup pending events: normal scenario
		def pendingEvents = ["roleGranted", "roleRevoked"]
		def mockPendingEventLogs = createMockPendingEventLogs(pendingEvents, 200L, "0xabc")
		setUpPendingEvent(mockPendingEventLogs)

		when: "The service starts"
		def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

		scheduler.schedule({
			AdhocHelper.stopBCMonitoring()
		}, 20, TimeUnit.SECONDS)
		commandLineRunner.run("-f")

		then: "No exceptions are thrown at service level"
		noExceptionThrown()

		and: "WARN level: Block delay warning is logged but service continues"
		assert logAppender.list.any {
			it.formattedMessage =~ /Block 1001 is delayed by more than \d+ seconds/ && it.level == Level.WARN
		}
		// Verify service continues after WARN - should have successful operations after delay warning
		assert logAppender.list.any {
			it.formattedMessage.contains("Success to register event") && it.level == Level.INFO &&
					it.getMDCPropertyMap().get("event_name") == "AddTokenByProvider" &&
					it.getMDCPropertyMap().get("tx_hash") == "0xdef456135" &&
					it.getMDCPropertyMap().get("block_height") == "1001" &&
					it.getMDCPropertyMap().get("trace_id") == "" &&
					it.getMDCPropertyMap().get("block_timestamp") == "**********" &&
					it.getMDCPropertyMap().get("log_index") == "1"
		}

		and: "ERROR level: Block height zero causes error and service restart"
		assert logAppender.list.any {
			it.formattedMessage.contains("Block height Number is zero") && it.level == Level.ERROR
		}
		// Verify service restart behavior - should see "Restarting bc monitoring" message
		assert logAppender.list.any {
			it.formattedMessage.contains("Restarting bc monitoring") && it.level == Level.ERROR
		}
		// Verify that despite ERROR and restart, service continues and processes other valid blocks
		assert logAppender.list.count {
			it.formattedMessage.contains("Success to register block number") && it.level == Level.INFO
		} >= 1  // At least one successful block registration despite the error

		and: "INFO level: Normal operations complete successfully"
		assert logAppender.list.any {
			it.formattedMessage.contains("Success to register block number") && it.level == Level.INFO
		}
		assert logAppender.list.any {
			it.formattedMessage.contains("Success to register event") && it.level == Level.INFO
		}
		assert logAppender.list.any {
			it.formattedMessage.contains("Success to process pending transactions") && it.level == Level.INFO
		}
		assert logAppender.list.any {
			it.formattedMessage.contains("Success to process new transactions") && it.level == Level.INFO
		}
	}
}
